//TODO: Reconsider how this is done and build proper domain layer, PolyData* needs renamed. Can Date fields just be numbers instead?
//      Rehydrating just for date objects is overkill. Separate things into full domain objects and 'jsonable' objects.

export type Serialized<T> = {
  [K in keyof T]
    : T[K] extends number
    ? number // Leave numbies untouched
    : T[K] extends Date
    ? string // Convert dates to strings
    : T[K] extends Array<infer U>
    ? Serialized<U>[] // Recursively serialize array elements
    : T[K] extends object
    ? Serialized<T[K]> // Recursively serialize objects
    : string; // Convert primitives to strings
};

export class PolyDataEvent {
  public id: string;
  public title: string;
  public description: string;
  public startDate: string | Date;
  public endDate: string | Date;
  public imageUrl: string;
  public volume: number;
  public markets: PolyDataMarket[];
  public commentParentId: string;
  public commentParentType: string;

  public constructor(data: PolyGammaEvent) {
    this.id = data.id;
    this.title = data.title;
    this.description = data.description;
    this.startDate = new Date(data.startDate);
    this.endDate = new Date(data.endDate);
    this.imageUrl = data.image;
    this.volume = data.volume;
    if (data.seriesSlug) {
      //Grab matching series id
      const parentId = data.series?.find(series => series.slug === data.seriesSlug)?.id ?? -1;
      if (parentId === -1)
        throw new Error(`Failed to find series ID for event ${data.title} (${data.id}) with series slug ${data.seriesSlug}`);

      this.commentParentId = parentId;
      this.commentParentType = 'Series';
    }
    else {
      this.commentParentId = data.id;
      this.commentParentType = 'Event';
    }

    //Parse markets
    this.markets = [];
    for (const marketData of data.markets) {
      this.markets.push(new PolyDataMarket(marketData));
    }
  }
}

export class PolyDataMarket {
  public id: string;
  public title: string;
  public conditionId: string;
  public volume: number;
  public shortName: string;
  public sortOrder: number;
  public spread: number;
  public tickSize: string;
  public isClosed: boolean;
  public isNegRisk: boolean;
  public outcomeNameA: string;
  public outcomeNameB: string;
  public bestAskA: number;
  public bestBidA: number;
  public bestAskB: number;
  public bestBidB: number;
  public assetIdA: string;
  public assetIdB: string;
  public bookLookupA: Record<string, PolyDataBookOrder> = {};
  public bookLookupB: Record<string, PolyDataBookOrder> = {};
  public bookA: PolyDataBookOrder[] = [];
  public bookB: PolyDataBookOrder[] = [];
  public positionA: PolyDataPosition | undefined;
  public positionB: PolyDataPosition | undefined;
  public activityHistory: ApiMarketActivityItem[] | undefined;
  public resolution: string;
  public isResolved: boolean;
  public isHidden: boolean;

  public constructor(data: PolyGammaMarket) {
    this.id = data.id;
    this.title = data.question;
    this.conditionId = data.conditionId;
    this.volume = parseFloat(data.volume);
    this.shortName = data.groupItemTitle;
    this.sortOrder = parseInt(data.groupItemThreshold);
    this.spread = data.spread;
    const clobTokenIds = data.clobTokenIds instanceof Array ? data.clobTokenIds : JSON.parse(data.clobTokenIds) as string[];
    this.assetIdA = clobTokenIds[0];
    this.assetIdB = clobTokenIds[1];
    this.tickSize = data.orderPriceMinTickSize.toString();
    this.isClosed = data.closed;
    this.isNegRisk = data.negRisk;
    this.resolution = data.description;
    this.isResolved = data.closed;
    this.isHidden = !data.active;

    //Parse outcome data (bug with poly causes some endpoints to return unparsed json data instead of array)
    if (!(data.outcomes instanceof Array)) {
      data.outcomes = JSON.parse(data.outcomes) as string[];
    }
    this.outcomeNameA = data.outcomes[0];
    this.outcomeNameB = data.outcomes[1];

    if (data.outcomePrices) {
      if (!(data.outcomePrices instanceof Array)) {
        data.outcomePrices = JSON.parse(data.outcomePrices) as string[];
      }
      this.bestAskA = this.bestBidA = parseFloat(data.outcomePrices[0]) || 0;
      this.bestAskB = this.bestBidB = parseFloat(data.outcomePrices[1]) || 0;
    }
    else {
      this.bestAskA = this.bestBidA = 0;
      this.bestAskB = this.bestBidB = 0;
    }
  }

  public setBookOrder(assetId: string, price: number, size: number, isBid: boolean): void {
    const priceStr = price.toString();
    const bookLookup = assetId === this.assetIdA ? this.bookLookupA : (assetId === this.assetIdB ? this.bookLookupB : undefined);
    const book = assetId === this.assetIdA ? this.bookA : (assetId === this.assetIdB ? this.bookB : undefined);
    if (!bookLookup) {
      throw new Error(`Invalid assetId: ${assetId} for market ${this.shortName} (${this.conditionId})`);
    }
    let order = bookLookup[priceStr];
    if (!order) {
      order = new PolyDataBookOrder();
      bookLookup[priceStr] = order;
      book!.push(order);
    }

    order.price = price;
    order.shares = size;
    order.isBid = isBid;

    if (size <= 0) {
      delete bookLookup[priceStr];
      const index = book!.indexOf(order);
      if (index !== -1) {
        book!.splice(index, 1);
      }
    }
  }

  public clearBook(assetId: string): void {
    if (assetId === this.assetIdA) {
      this.bookLookupA = {};
      this.bookA = [];
    }
    else if (assetId === this.assetIdB) {
      this.bookLookupB = {};
      this.bookB = [];
    }
    else {
      throw new Error(`Invalid assetId: ${assetId} for market ${this.shortName} (${this.conditionId})`);
    }
  }

  public sortBooks(): void {
    this.bookA.sort((a, b) => b.price - a.price);
    this.bookB.sort((a, b) => b.price - a.price);
  }

  /**
   * Simulates a market sell of given asset and returns the total value of the sell.
   */
  public simulateMarketSell(assetId: string, shares: number): number {
    //NOTE: This function assumes book is sorted descending
    let book = this.bookA;
    let bestBid = this.bestBidA;
    if (assetId === this.assetIdB) {
      book = this.bookB;
      bestBid = this.bestBidB;
    }

    const bestBidIndex = book.findIndex((priceLevel) => priceLevel.price === bestBid);
    if (bestBidIndex === -1)
      return 0;
    //Get avg price of sample shares as if selling at bestBidIndex upwards
    let totalSharesSold = 0;
    let totalMade = 0;
    for (let i = bestBidIndex; i < book.length; i++) {
      const priceLevel = book[i];
      if (!priceLevel.isBid)
        break;
      const sellShares = Math.min(shares - totalSharesSold, priceLevel.shares);
      totalSharesSold += sellShares;
      totalMade += sellShares * priceLevel.price;
      if (totalSharesSold >= shares)
        break;
    }

    return totalMade;
  }

  public getBidAskSpread(assetId: string) : { bestBid: number, bestAsk: number, spread: number } {
    const orderBook = assetId === this.assetIdA ? this.bookLookupA : (assetId === this.assetIdB ? this.bookLookupB : undefined);
    if (!orderBook) {
      throw new Error(`Invalid assetId: ${assetId} for market ${this.shortName} (${this.conditionId})`);
    }

    let bestBid = 0;
    let bestAsk = 1;
    for (const priceStr in orderBook) {
      const order = orderBook[priceStr];
      if (order.isBid && order.price > bestBid) {
        bestBid = order.price;
      }
      else if (!order.isBid && order.price < bestAsk) {
        bestAsk = order.price;
      }
    }

    return { bestBid, bestAsk, spread: bestAsk - bestBid };
  }

  public getBestBid(assetId: string): number {
    const orderBook = assetId === this.assetIdA ? this.bookLookupA : (assetId === this.assetIdB ? this.bookLookupB : undefined);
    if (!orderBook) {
      throw new Error(`Invalid assetId: ${assetId} for market ${this.shortName} (${this.conditionId})`);
    }

    let bestBid = 0;
    for (const priceStr in orderBook) {
      const order = orderBook[priceStr];
      if (order.isBid && order.price > bestBid) {
        bestBid = order.price;
      }
    }

    return bestBid;
  }

  public getBestAsk(assetId: string): number {
    const orderBook = assetId === this.assetIdA ? this.bookLookupA : (assetId === this.assetIdB ? this.bookLookupB : undefined);
    if (!orderBook) {
      throw new Error(`Invalid assetId: ${assetId} for market ${this.shortName} (${this.conditionId})`);
    }

    let bestAsk = 1;
    for (const priceStr in orderBook) {
      const order = orderBook[priceStr];
      if (!order.isBid && order.price < bestAsk) {
        bestAsk = order.price;
      }
    }

    return bestAsk;
  }

  /**
   * Returns the midpoint price between this class's bestAskA and bestBidA properties. Considers tick size when rounding.
   * @param roundUp If true uses Math.ceil to round to ticksize, otherwise uses Math.floor.
   * @returns 
   */
  public getMidpointA(roundUp: boolean): number {
    const mul = (this.tickSize == "0.01" ? 100 : 1000);
    return ((roundUp ? Math.ceil : Math.floor)(((this.bestAskA + this.bestBidA)/2) * mul))/mul;
  }

  public lookupAssetName(assetId: string): string {
    return this.assetIdA === assetId ? this.outcomeNameA : (this.assetIdB === assetId ? this.outcomeNameB : 'ERR')
  }

  public lookupAssetId(assetName: string): string {
    return this.outcomeNameA === assetName ? this.assetIdA : (this.outcomeNameB === assetName ? this.assetIdB : 'ERR');
  }

  public roundToTickSize(price: number): number {
    return Number(price.toFixed(this.tickSize === "0.01" ? 2 : 3));
  }

  public hasPosition(): boolean {
    return this.positionA !== undefined || this.positionB !== undefined;
  }
}

export class PolyDataBookOrder {
  public price: number = 0;
  public shares: number = 0;
  public isBid: boolean = false;
}

export class PolyDataPosition {
  public marketCondId!: string;
  public shares!: number;
  public avgPrice!: number;
  public cost!: number;
  public outcome!: string;
  public assetId!: string;

  public constructor(data?: PolyApiPosition) {
    if (data) {
      this.marketCondId = data.conditionId;
      this.shares = Number(data.size);
      this.avgPrice = Number(data.avgPrice);
      this.cost = Number(data.initialValue);
      this.outcome = data.outcome;
      this.assetId = data.asset;
    }
  }
}

export class PolyDataOpenOrder {
  public id!: string;
  public assetId!: string;
  public market!: string;
  public isBuy!: boolean;
  public price!: number;
  public sharesTotal!: number;
  public sharesMatched!: number;

  public static fromApiJson(data: PolyClobOpenOrder): PolyDataOpenOrder {
    const order = new PolyDataOpenOrder();
    order.id = data.id;
    order.assetId = data.asset_id;
    order.market = data.market;
    order.isBuy = data.side === "BUY";
    order.price = Number(data.price);
    order.sharesTotal = Number(data.original_size);
    order.sharesMatched = Number(data.size_matched);

    return order;
  }

  public static fromWSJson(data: PolyWSOrder): PolyDataOpenOrder {
    const order = new PolyDataOpenOrder();
    order.id = data.id;
    order.assetId = data.asset_id;
    order.market = data.market;
    order.isBuy = data.side === "BUY";
    order.price = Number(data.price);
    order.sharesTotal = Number(data.original_size);
    order.sharesMatched = Number(data.size_matched);

    return order;
  }
}

export class PolyDataComment {
  public id: string;
  public text: string;
  public userAddress: string;
  public createdAt: Date;
  public profile: PolyGammaCommentProfile;
  public reportCount: number;
  public reactionCount: number;
  public reactions: PolyGammaCommentReaction[];
  public replyAddress: string | undefined;
  public parentCommentId: string | undefined;
  public parentComment: PolyDataComment | undefined;
  public childComments: PolyDataComment[] | undefined;

  public constructor(data: PolyGammaComment) {
    this.id = data.id;
    this.text = data.body;
    this.parentCommentId = data.parentCommentID;
    this.userAddress = data.userAddress;
    this.createdAt = new Date(data.createdAt);
    this.profile = data.profile;
    this.reportCount = data.reportCount;
    this.reactionCount = data.reactionCount;
    this.reactions = data.reactions || [];
    this.replyAddress = data.replyAddress;
  }
}

//TODO: Make this should be a json friendly class such that conversion isn't required between this and json.
//      Only numbers, strings, arrays, and plain objects are allowed.
export class PolyDataHistory {
  public assetId: string;
  public price: number;
  public shares: number;
  public side: HistorySide;
  public time: Date;
  public isClientside: boolean;
  public uniqueId: number;

  public static nextUniqueId = 1;

  public constructor(history: ApiHistoryItem);
  public constructor(history: Serialized<PolyDataHistory>);
  public constructor(assetId: string, price: number, shares: number, side: HistorySide, time: Date, isClientside?: boolean);
  public constructor(history: ApiHistoryItem | Required<PolyDataHistory> | Serialized<PolyDataHistory> | string, price?: number, shares?: number, side?: HistorySide, time?: Date, isClientside?: boolean) {
    this.isClientside = false;
    this.uniqueId = PolyDataHistory.nextUniqueId++;

    if (typeof history === "string") {
      this.assetId = history;
      this.price = price!;
      this.shares = shares!;
      this.side = side!;
      this.time = time!;
      this.isClientside = !!isClientside;
    }
    else if ((history as ApiHistoryItem).asset_id) {
      history = history as ApiHistoryItem;
      this.assetId = history.asset_id;
      this.price = history.price;
      this.shares = history.shares;
      this.side = history.side;
      this.time = new Date(history.time);
    }
    else {
      history = history as Serialized<PolyDataHistory>;
      this.assetId = history.assetId;
      this.price = history.price;
      this.shares = history.shares;
      this.side = history.side;
      this.time = new Date(history.time);
    }
  }
}

export interface PolyWSBook {
  market: string | undefined;
  asset_id: string | undefined;
  timestamp: string | undefined;
  hash: string | undefined;
  bids: PolyWSBookOrder[] | undefined;
  asks: PolyWSBookOrder[] | undefined;
}

export interface PolyWSBookOrder {
  price: string;
  size: string;
}

export interface PolyWSPriceChange {
  asset_id: string | undefined;
  changes: PolyWSPriceChangeOrder[] | undefined;
  hash: string | undefined;
  market: string | undefined;
  timestamp: string | undefined;
}

/**
 * Represents a trade. (Comptaible with clob client's Trade interface)
 */
export interface PolyWSTrade {
  /**
   * Asset id (token ID) of taker order (market order)
   */
  asset_id: string;
  /**
   * Trade ID
   */
  id: string;
  /**
   * Time of last update to trade (eg 1672290701)
   */
  last_update: string;
  /**
   * Array of maker order match details
   */
  maker_orders: PolyWSMakerOrder[] | null;
  /**
   * Market identifier (condition ID)
   */
  market: string;
  /**
   * Time trade was matched
   */
  matchtime: string;
  /**
   * Outcome (Yes, No, etc)
   */
  outcome: string;
  /**
   * API key of event owner
   */
  owner: string;
  /**
   * Price
   */
  price: string;
  /**
   * BUY/SELL
   */
  side: string;
  /**
   * Size
   */
  size: string;
  /**
   * Trade status (MATCHED, MINED, CONFIRMED, RETRYING, FAILED)
   * Note: Order is MATCHED -> MINED -> CONFIRMED
   */
  status: string;
  /**
   * ID of taker order
   */
  taker_order_id: string;
  /**
   * TAKER/MAKER - which side of the trade we are on (are we TAKING from existing order or MAKING an order for someone to take from)
   * NOTE: If we are MAKER, this trade describes the order from the other trader's perspective (SIDE WILL BE OPPOSITE OF OUR ORDER)
   * e.g. If we are MAKER and we are BUYing, the other trader is SELLing and this trade will have outcome = 'SELL'
   */
  trader_side: string;
  /**
   * Time of event (eg 1672290701)
   */
  timestamp: number;
  /**
   * API key of trade owner
   */
  trade_owner: string;
  bucket_index: string;
}

export interface PolyWSMakerOrder {
  /**
   * Asset id of the maker order
   */
  asset_id: string;
  /**
   * Amount of maker order matched in trade
   */
  matched_amount: string;
  /**
   * Maker order ID
   */
  order_id: string;
  /**
   * Outcome (YES, NO, etc)
   */
  outcome: string;
  /**
   * Api key of owner of maker order
   */
  owner: string;
  /**
   * Price of maker order
   */
  price: string;
  /**
   * Wallet address of order maker
   */
  maker_address: string;
}

export interface PolyWSOrder {
  /**
   * Asset id (token ID) of order
   */
  asset_id: string;
  /**
   * Array of ids referencing trades that the order has been included in
   * eg 'b18e9004-fefb-485f-990a-3fed440e91db'
   */
  associate_trades: string[] | null;
  /**
   * Order ID
   */
  id: string;
  /**
   * Condition ID of market
   */
  market: string;
  /**
   * Owner of order
   */
  order_owner: string;
  /**
   * Order type (GTC, IOC, FOK)
   */
  order_type: string;
  /**
   * Original order size
   */
  original_size: string;
  /**
   * Outcome string
   */
  outcome: string;
  /**
   * API key of wwner of order
   */
  owner: string;
  /**
   * Price of order
   */
  price: string;
  /**
   * BUY/SELL
   */
  side: string;
  /**
   * Size (shares) of order that has been matched
   */
  size_matched: string;
  /**
   * Time of event (eg 1672290701)
   */
  timestamp: string;
  /**
   * Time of order creation (eg 1672290701)
   */
  created_at: string;
  /**
   * PLACEMENT/UPDATE/CANCELLATION
   * Note: Update is for open orders that were MATCHED at least some amount.
   */
  type: string;
  /**
   * CANCELED/MATCHED/LIVE
   */
  status: string;
}

export interface PolyWSPriceChangeOrder {
  price: string;
  side: string;
  size: string;
}

export interface PolyWSTickSizeChange {
  asset_id: string;
  market: string;
  old_tick_size: string;
  new_tick_size: string;
  side: string;
  timestamp: string;
}

export interface PolyWSLastTradePrice {
  asset_id: string;
  event_type: string;
  fee_rate_bps: string;
  /**
   * Condition ID
   */
  market: string;
  price: string;
  /**
   * BUY/SELL
   */
  side: string;
  size: string;
  /**
   * Unix timestamp (eg 1672290701). Fits into new Date().
   */
  timestamp: string;
}

export interface PolyPostOrderResponse {
  /**
   * boolean indicating server-side error (if success == false -> server-side error)
   */
  success: boolean;
  /**
   * error message in case of unsuccessful placement (in case success == true && errorMsg != '' -> client-side error, the reason is in errorMsg)
   */
  errorMsg: string;
  /**
   * error message that occurs for critical (http ??) errors I guess? Use errorMsg, this will be packed into it.
   */
  error: string | undefined;
  /**
   * id of order
   */
  orderID: string;
  /**
   * hash of settlement transaction if order was marketable and triggered a match action, null otherwise (no matches)
   */
  transactionsHashes: string[];
  /**
   * order status (live, matched, ...)
   */
  status: string;
  takingAmount: string;
  makingAmount: string;
}

export interface PolyClobOpenOrder {
  id: string;
  /**
   * LIVE (and others I guess?)
   */
  status: string;
  owner: string;
  maker_address: string;
  /**
   * Condition ID
   */
  market: string;
  asset_id: string;
  side: string;
  /**
   * Order size
   */
  original_size: string;
  /**
   * Amount of order that has been filled so far
   */
  size_matched: string;
  price: string;
  associate_trades: string[];
  /**
   * Outcome name (Yes, No, etc)
   */
  outcome: string;
  created_at: number;
  expiration: string;
  /**
   * GTC, IOC, FOK
   */
  order_type: string;
}

export interface PolyCancelOrderResponse {
  /**
   * Order IDs that were successfully canceled.
   */
  canceled: string[];
  /**
   * Order IDs that failed to cancel. Key is order ID, value is error message.
   */
  not_canceled: Record<string, string>;
}

export interface PolyUserBalanceResponse {
  balance: number;
}

export interface PolyElonTweetResponse {
  tweetCount: number;
  lastTweetTime: string;
}

export interface PolyGammaEvent {
  id: string;
  ticker: string;
  slug: string;
  title: string;
  description: string;
  resolutionSource: string;
  startDate: string;
  creationDate: string;
  endDate: string;
  image: string;
  icon: string;
  active: boolean;
  closed: boolean;
  archived: boolean;
  new: boolean;
  featured: boolean;
  restricted: boolean;
  liquidity: number;
  volume: number;
  openInterest: number;
  createdAt: string;
  updatedAt: string;
  competitive: number;
  volume24hr: number;
  enableOrderBook: boolean;
  liquidityClob: number;
  negRisk: boolean;
  negRiskMarketID: string;
  commentCount: number;
  cyom: boolean;
  showAllOutcomes: boolean;
  showMarketImages: boolean;
  enableNegRisk: boolean;
  automaticallyActive: boolean;
  startTime: string;
  /**
   * If specified, this event belongs to parent series. (Comments should are stored on series instead of event if present)
   */
  seriesSlug: string | undefined;
  gmpChartMode: string;
  negRiskAugmented: boolean;
  tweetCount: number;
  markets: PolyGammaMarket[];
  series: PolyGammaSeries[] | undefined;
}

export interface PolyGammaMarket {
  id: string;
  question: string;
  conditionId: string;
  slug: string;
  resolutionSource: string;
  endDate: string;
  startDate: string;
  image: string;
  icon: string;
  description: string;
  outcomes: string[] | string;
  outcomePrices: string[] | string;
  volume: string;
  /**
   * false for weird "hidden" markets; true otherwise
   */
  active: boolean;
  /**
   * true for resolved markets; false otherwise
   */
  closed: boolean;
  marketMakerAddress: string;
  createdAt: string;
  updatedAt: string;
  closedTime: string;
  new: boolean;
  featured: boolean;
  submitted_by: string;
  archived: boolean;
  resolvedBy: string;
  restricted: boolean;
  /**
   * Market short name
   */
  groupItemTitle: string;
  groupItemThreshold: string;
  questionID: string;
  umaEndDate: string;
  enableOrderBook: boolean;
  orderPriceMinTickSize: number;
  orderMinSize: number;
  umaResolutionStatus: string;
  volumeNum: number;
  endDateIso: string;
  startDateIso: string;
  hasReviewedDates: boolean;
  clobTokenIds: string[] | string;
  umaBond: string;
  umaReward: string;
  volumeClob: number;
  acceptingOrders: boolean;
  negRisk: boolean;
  ready: boolean;
  funded: boolean;
  acceptingOrdersTimestamp: string;
  cyom: boolean;
  pagerDutyNotificationEnabled: boolean;
  approved: boolean;
  rewardsMinSize: number;
  rewardsMaxSpread: number;
  spread: number;
  automaticallyResolved: boolean;
  oneDayPriceChange: number;
  lastTradePrice: number;
  bestAsk: number;
  automaticallyActive: boolean;
  clearBookOnStart: boolean;
  seriesColor: string;
  showGmpSeries: boolean;
  showGmpOutcome: boolean;
  manualActivation: boolean;
  /**
   * Present on some apis like gamma market endpoint.
   */
  events: PolyGammaEvent[];
}

export interface PolyGammaSeries {
  id: string;
  ticker: string;
  slug: string;
  title: string;
  seriesType: string;
  recurrence: string;
  image: string;
  icon: string;
  active: boolean;
  closed: boolean;
  archived: boolean;
  new: boolean;
  featured: boolean;
  restricted: boolean;
  publishedAt: string;
  /**
   * Iso date string (eg 2025-02-12T14:22:30.685622Z)
   */
  createdAt: string;
  /**
   * Iso date string (eg 2025-02-12T14:22:30.685622Z)
   */
  updatedAt: string;
  commentsEnabled: boolean;
  competitive: number;
  volume24hr: number;
  volume: number;
  liquidity: number;
  /**
   * Iso date string (eg 2025-02-12T14:22:30.685622Z)
   */
  startDate: string;
  commentCount: number;
}

export interface PolyPriceHistory {
  /**
   * Timeseries data for price history
   */
  history: PolyPriceHistoryItem[];
}

export interface PolyPriceHistoryItem {
  /**
   * UTC timestamp
   */
  t: number;
  /**
   * Price
   */
  p: number;
}

export interface PolyGammaComment {
  id: string;
  /**
   * Comment text
   */
  body: string;
  /**
   * Comment ID of parent comment if this is a reply
   */
  parentCommentID: string | undefined;
  /**
   * One of 'Series' or 'Event'. Determines whether comment belongs to specific event or the event's series entity.
   */
  parentEntityType: string;
  /**
   * Event ID this belongs to
   */
  parentEntityID: string;
  /**
   * Base (not proxy) wallet address
   */
  userAddress: string;
  /**
   * Iso date string (eg 2025-02-12T14:22:30.685622Z)
   */
  createdAt: string;
  /**
   * Iso date string (eg 2025-02-12T14:22:31.525476Z)
   */
  updatedAt: string;
  profile: PolyGammaCommentProfile;
  /**
   * Number of reports made against this comment
   */
  reportCount: number;
  /**
   * Number of reactions made on comment
   */
  reactionCount: number;
  reactions: PolyGammaCommentReaction[] | undefined;
  /**
   * Base (not proxy) address of user this comment is replying to. (eg @Username ...)
   */
  replyAddress: string | undefined;
}

export interface PolyGammaCommentProfile {
  /**
   * Public username
   */
  name: string;
  /**
   * (?) seems to be random word string
   */
  pseudonym: string;
  displayUsernamePublic: boolean;
  proxyWallet: string;
  /**
   * User wallet address (not proxy)
   */
  baseAddress: string;
  positions: {
    /**
     * Asset ID (token ID)
     */
    tokenId: string;
    /**
     * Shares but must divide by 1,000,000
     */
    positionSize: string;
  }[];
  profileImage: string | undefined;
}

export interface PolyGammaCommentReaction {
  id: string;
  commentID: string;
  /**
   * HEART, etc
   */
  reactionType: string;
  /**
   * Base (not proxy) wallet address
   */
  userAddress: string;
  profile: {
    proxyWallet: string;
  };
}

export interface PolyPostCommentResponse {
  /**
   * Comment ID
   */
  id: string;
  /**
   * Iso date string (eg 2025-02-12T14:22:30.685622Z)
   */
  createdAt: string;
}

export interface PolyPostReactionResponse {
  /**
   * Reaction ID
   */
  id: string;
  commentID: string;
  reactionType: string;
  userAddress: string;
}

export interface PolyUserProfileResponse {
  /**
   * Iso date string (eg 2025-02-12T14:22:30.685622Z)
   */
  createdAt: string;
  proxyWallet: string;
  profileImage: string | undefined;
  displayUsernamePublic: boolean;
  bio: string;
  name: string;
  users: {
    id: string;
    creator: boolean;
    mod: boolean;
  }[];
}

export interface PolyGammaHistoryItem {
  proxyWallet: string;
  /**
   * Unix timestamp in seconds (multiply by 1000 to use with new Date())
   */
  timestamp: number;
  conditionId: string;
  /**
   * TRADE, CONVERSION, SPLIT, REWARD, REDEEM
   * If not TRADE then asset, side, and outcome will be empty strings.
   * REWARD has no conditionId either.
   */
  type: string;
  size: number;
  usdcSize: number;
  transactionHash: string;
  price: number;
  asset: string;
  side: string;
  outcomeIndex: number;
  title: string;
  slug: string;
  icon: string;
  eventSlug: string;
  outcome: string;
  name: string;
  pseudonym: string;
  bio: string;
  profileImage: string;
  profileImageOptimized: string;
}

export interface PolyDataTrade {
  proxyWallet: string;
  /**
   * BUY or SELL
   */
  side: string;
  asset: string;
  conditionId: string;
  size: number;
  price: number;
  /**
   * Unix timestamp of trade in seconds (must multiply by 1000 to use with new Date())
   */
  timestamp: number;
  /**
   * Readable title of event
   */
  title: string;
  slug: string;
  icon: string;
  eventSlug: string;
  /**
   * Yes, No, etc
   */
  outcome: string;
  outcomeIndex: number;
  /**
   * Username
   */
  name: string;
  pseudonym: string;
  bio: string;
  profileImage: string;
  profileImageOptimized: string;
  transactionHash: string;
}

export interface PolyApiPosition {
  proxyWallet: string;
  /**
   * Asset ID (token ID)
   */
  asset: string;
  conditionId: string;
  /**
   * Shares currently held
   */
  size: number;
  /**
   * Average price paid
   */
  avgPrice: number;
  /**
   * Initial cost in USDC
   */
  initialValue: number;
  /**
   * Current market worth in USDC
   */
  currentValue: number;
  cashPnl: number;
  percentPnl: number;
  /**
   * Total shares bought over lifespan of the market
   */
  totalBought: number;
  realizedPnl: number;
  percentRealizedPnl: number;
  curPrice: number;
  redeemable: boolean;
  mergeable: boolean;
  /**
   * Event title (readable)
   */
  title: string;
  slug: string;
  /**
   * Url to event icon image
   */
  icon: string;
  eventSlug: string;
  outcome: string;
  outcomeIndex: number;
  /**
   * Opposite outcome (Yes, No, etc)
   */
  oppositeOutcome: string;
  /**
   * Asset ID of opposite outcome
   */
  oppositeAsset: string;
  /**
   * Simple iso date string (eg 2025-02-28)
   */
  endDate: string;
  negativeRisk: boolean;
}

export interface ApiHistoryItem {
  condition_id: string;
  price: number;
  shares: number;
  asset_id: string;
  market_id: number;
  polyuser_history_id: number;
  side: number;
  time: number;
  user_id: number;
}

export interface ApiMarketActivityItem {
  condition_id: string;
  time: number;
  price: number;
  shares: number;
  side: number;
  market_id: number;
  asset_id: string;
  market_history_id: number;
  user_id: number | null;
  user_proxy_wallet: string;
}

export type PolyReactionType = "HEART";

export enum HistorySide {
  Buy = 0,
  Sell = 1,
  Convert = 2,
  Redeem = 3,
  Split = 4,
  Merge = 5,
  Reward = 6,
  Unknown = 99
};

export interface PolyApiTag {
  id: string;
  label: string;
  slug: string;
  event_count: number;
}

export interface PolySearchResponse {
  events: PolyGammaEvent[];
  tags: PolyApiTag[];
  hasMore: boolean;
}

export function stringToHistorySide(side: string): HistorySide {
  switch (side.toLowerCase()) {
    case "buy": return HistorySide.Buy;
    case "sell": return HistorySide.Sell;
    case "conversion": return HistorySide.Convert;
    case "redeem": return HistorySide.Redeem;
    case "split": return HistorySide.Split;
    case "merge": return HistorySide.Merge;
    case "reward": return HistorySide.Reward;
    default: return HistorySide.Unknown;
  }
}

export function historySideToString(side: HistorySide): string {
  switch (side) {
    case HistorySide.Buy: return "BUY";
    case HistorySide.Sell: return "SELL";
    case HistorySide.Convert: return "CONVERT";
    case HistorySide.Redeem: return "REDEEM";
    case HistorySide.Split: return "SPLIT";
    case HistorySide.Merge: return "MERGE";
    case HistorySide.Reward: return "REWARD";
    default: return "UNKNOWN";
  }
}

export enum PriceHistoryInterval {
  MAX = "max",
  ONE_WEEK = "1w",
  ONE_DAY = "1d",
  SIX_HOURS = "6h",
  ONE_HOUR = "1h"
}

export type PolySearchEventSort = "volume_24hr" | "competitive" | "start_date" | "liquidity" | "volume" | "end_date";
export type PolySearchEventStatus = "active" | "resolved" | "all";
export type PolySearchEventFrequency = "daily" | "weekly" | "monthly" | "all";
export enum PolyPositionSort {
  TITLE = "TITLE",
  COST = "INITIAL",
  PRICE = "PRICE",
  SHARES = "TOKENS",
  VALUE = "CURRENT"
}