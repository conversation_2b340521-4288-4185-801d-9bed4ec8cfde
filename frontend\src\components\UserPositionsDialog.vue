<template>
  <q-dialog v-model:model-value="appStore.userPositionsDialog.isShown" class="dialog-positions">
    <q-card class="positions-main">
      <!-- Header -->
      <div class="positions-header">
        <div class="header-left">
          <q-avatar class="user-avatar">
            <img v-if="userProfile?.profileImage" :src="userProfile.profileImage" loading="lazy" decoding="async">
            <div v-else class="profile-none"></div>
          </q-avatar>
          <!--  User name -->
          <div class="text-h6">
            {{ userProfile?.name ? `${userProfile.name}'s Positions` : "User Positions" }}
          </div>
          <div v-if="userProfit !== null" class="user-pnl">({{ formatCurrency(userProfit, 0) }})</div>
        </div>
        <div class="header-right">
          <q-btn
            v-if="showOrdersButton"
            :label="showOrders ? 'Hide Orders' : 'Orders'"
            no-caps
            flat
            class="btn-orders"
            @click="onClickToggleOrders"
          />
          <q-btn icon="close" size="sm" flat @click="onClickClose" />
        </div>
      </div>

      <!-- Loading state -->
      <div v-if="isLoading" class="loading-container">
        <q-spinner size="lg" />
        <div class="q-mt-sm">Loading positions...</div>
      </div>

      <!-- No positions state -->
      <div v-else-if="sortedPositions.length === 0" class="no-positions-container">
        No positions found
      </div>

      <!-- Orders section -->
      <q-slide-transition>
        <div v-show="showOrders" class="orders-section">
          <UserOrdersInterface :userProxyWallet="appStore.userPositionsDialog.userProxyWallet" />
        </div>
      </q-slide-transition>

      <!-- Positions content -->
      <div class="positions-content">
        <!-- Column headers -->
        <div class="positions-header-row">
          <div class="header-icon-space"></div>
          <div class="header-data-section">
            <div class="header-shares-group" @click="onClickSort('size')" :class="getSortClass('size')">
              Shares
              <q-icon :name="getSortIcon('size')" size="xs" />
            </div>
            <div class="header-price-group" @click="onClickSort('avgPrice')" :class="getSortClass('avgPrice')">
              Price
              <q-icon :name="getSortIcon('avgPrice')" size="xs" />
            </div>
            <div class="header-cost-group">
              Cost
            </div>
            <div class="header-pnl-group" @click="onClickSort('cashPnl')" :class="getSortClass('cashPnl')">
              P&L
              <q-icon :name="getSortIcon('cashPnl')" size="xs" />
            </div>
            <div class="header-value-group" @click="onClickSort('currentValue')" :class="getSortClass('currentValue')">
              Value
              <q-icon :name="getSortIcon('currentValue')" size="xs" />
            </div>
          </div>
        </div>

        <!-- Position rows -->
        <div v-for="position in sortedPositions" :key="position.asset" class="position-group">
          <!-- Position container: flex row with icon and data -->
          <div class="position-container">
            <!-- Position icon (left side) -->
            <div class="position-icon">
              <img :src="position.icon || '/img/default_event.webp'" :alt="position.title" />
            </div>

            <!-- Data container: flex column with title and position row -->
            <div class="data-container">
              <!-- Market title -->
              <router-link :to="`/events/${position.eventSlug}`" class="market-title-link">
                <div class="market-title">
                  {{ position.title }}
                </div>
              </router-link>

              <!-- Position data row -->
              <div class="position-data-row">
                <div class="row-shares" :class="getOutcomeClass(position.outcome)">{{ formatDecimal(position.size, 0, true) }}</div>
                <div class="row-outcome" :class="getOutcomeClass(position.outcome)">
                  {{ position.outcome }}
                </div>
                <div class="row-avgprice" :class="getOutcomeClass(position.outcome)">@ {{ formatCents(position.avgPrice, 0) }}</div>
                <div class="row-arrow">→</div>
                <div class="row-curprice">{{ formatCents(position.curPrice, 0) }}</div>
                <div class="row-cost">{{ formatCurrency(position.initialValue, 0) }}&nbsp;</div>
                <div class="row-pnl-cash" :class="getPnlClass(position.cashPnl)">
                  {{ position.cashPnl >= 0 ? '+' : '' }}{{ formatCurrency(position.cashPnl, 0) }}
                </div>
                <div class="row-pnl-percent" :class="getPnlClass(position.cashPnl)">
                  &nbsp;({{ position.percentPnl >= 0 ? '+' : '' }}{{ formatDecimal(position.percentPnl, 1, true) }}%)&nbsp;
                </div>
                <div class="row-value">{{ formatCurrency(position.currentValue, 0) }}&nbsp;</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </q-card>
  </q-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import { PolyApiPosition, PolyUserProfileResponse } from "@shared/api-dataclasses-shared";
import { formatCurrency, formatCents, formatDecimal } from "src/utils";
import { useApi } from "src/api";
import { useAppStore } from "src/stores/app-store";
import { useUserStore } from "src/stores/user-store";
import UserOrdersInterface from "./UserOrdersInterface.vue";

const appStore = useAppStore();
const userStore = useUserStore();

const api = useApi();
const isLoading = ref(false);
const positions = ref<PolyApiPosition[]>([]);
const userProfile = ref<PolyUserProfileResponse | null>(null);
const userProfit = ref<number | null>(null);
const sortBy = ref("currentValue");
const sortDesc = ref(true);
const showOrders = ref(false);
const dynamicWidths = ref({
  shares: 55,
  outcome: 60,
  cost: 60,
  value: 60,
  pnlCash: 50
});

const showOrdersButton = computed(() => {
  //Only show orders button if viewing current user's positions
  return appStore.userPositionsDialog.userProxyWallet === userStore.storage.walletAddress;
});

const sortedPositions = computed(() => {
  if (!positions.value.length) return [];

  //Sort positions directly
  const sorted = [...positions.value].sort((a, b) => {
    let aVal: any, bVal: any;
    aVal = (a as any)[sortBy.value];
    bVal = (b as any)[sortBy.value];

    if (typeof aVal === "string") {
      aVal = aVal.toLowerCase();
      bVal = bVal.toLowerCase();
    }

    if (aVal < bVal) return sortDesc.value ? 1 : -1;
    if (aVal > bVal) return sortDesc.value ? -1 : 1;
    return 0;
  });

  return sorted;
});

async function loadDialog() {
  sortDesc.value = true;
  sortBy.value = "currentValue";
  showOrders.value = false; //Reset orders view when dialog opens

  fetchPositions();
  fetchUserProfit(); //Async, not awaited
}

async function fetchPositions() {
  if (!appStore.userPositionsDialog.userProxyWallet) return;

  try {
    //Start fetching user profile (don't await yet)
    const profilePromise = api.getUserProfile(appStore.userPositionsDialog.userProxyWallet);

    //Fetch positions
    const positionsData = await api.getPositions(appStore.userPositionsDialog.userProxyWallet);
    positions.value = positionsData;

    //Calculate dynamic widths after data is loaded
    calculateDynamicWidths();

    //Finally await the user profile
    userProfile.value = await profilePromise;
  }
  finally {
    isLoading.value = false;
  }
}

async function fetchUserProfit() {
  if (!appStore.userPositionsDialog.userProxyWallet) return;

  userProfit.value = null;
  const profit = await api.getUserProfit(appStore.userPositionsDialog.userProxyWallet);
  userProfit.value = profit;
}

function onClickSort(column: string) {
  if (sortBy.value === column) {
    sortDesc.value = !sortDesc.value;
  }
  else {
    sortBy.value = column;
    //Default to ascending for market/title, descending for all others
    sortDesc.value = column === "title" ? false : true;
  }
}

function getSortClass(column: string) {
  return {
    "sort-active": sortBy.value === column,
    "cursor-pointer": true
  };
}

function getSortIcon(column: string) {
  if (sortBy.value !== column) return "unfold_more";
  return sortDesc.value ? "keyboard_arrow_down" : "keyboard_arrow_up";
}

function getOutcomeClass(outcome: string) {
  const lower = outcome.toLowerCase();
  if (lower.includes("yes") || lower.includes("true")) return "text-yes bg-yes";
  if (lower.includes("no") || lower.includes("false")) return "text-no bg-no";
  return "";
}

function getPnlClass(pnl: number) {
  if (pnl > 0) return "text-yes";
  if (pnl < 0) return "text-no";
  return "";
}

function measureTextWidth(text: string): number {
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');
  if (!context) return 0;

  //Match the font used in the cells
  context.font = '13px monospace';
  return context.measureText(text).width;
}

function calculateDynamicWidths() {
  if (!positions.value.length) return;

  const sharesPadding = 6;
  const outcomePadding = 6;
  const costPadding = 3;
  const valuePadding = 15;
  const pnlCashPadding = 2;

  const sharesWidths = positions.value.map(p => measureTextWidth(formatDecimal(p.size, 0, true)));
  const outcomeWidths = positions.value.map(p => measureTextWidth(p.outcome));
  const costWidths = positions.value.map(p => measureTextWidth(formatCurrency(p.initialValue, 0)));
  const valueWidths = positions.value.map(p => measureTextWidth(formatCurrency(p.currentValue, 0)));
  const pnlCashWidths = positions.value.map(p => {
    const formatted = `${p.cashPnl >= 0 ? '+' : ''}${formatCurrency(p.cashPnl, 0)}`;
    return measureTextWidth(formatted);
  });

  dynamicWidths.value = {
    shares: Math.max(...sharesWidths, 30) + sharesPadding, //Min 30px, no padding here
    outcome: Math.max(...outcomeWidths, 30) + outcomePadding, //Min 30px, no padding here
    cost: Math.max(...costWidths, 40) + costPadding, //Min 40px, no padding here
    value: Math.max(...valueWidths, 40) + valuePadding, //Min 40px, no padding here
    pnlCash: Math.max(...pnlCashWidths, 40) + pnlCashPadding //Min 40px, no padding here
  };

  //Apply to CSS custom properties
  document.documentElement.style.setProperty('--shares-width', `${dynamicWidths.value.shares}px`);
  document.documentElement.style.setProperty('--outcome-width', `${dynamicWidths.value.outcome}px`);
  document.documentElement.style.setProperty('--cost-width', `${dynamicWidths.value.cost}px`);
  document.documentElement.style.setProperty('--value-width', `${dynamicWidths.value.value}px`);
  document.documentElement.style.setProperty('--pnl-cash-width', `${dynamicWidths.value.pnlCash}px`);

  //Update shares group header width (shares + outcome + padding for header)
  const sharesGroupWidth = dynamicWidths.value.shares + dynamicWidths.value.outcome;
  document.documentElement.style.setProperty('--shares-group-width', `${sharesGroupWidth}px`);

  //Update price group header width (avgprice + arrow + curprice)
  const priceGroupWidth = 40 + 16 + 60; //avgprice + arrow + curprice
  document.documentElement.style.setProperty('--price-group-width', `${priceGroupWidth}px`);

  //Update cost group header width (just cost column)
  const costGroupWidth = dynamicWidths.value.cost;
  document.documentElement.style.setProperty('--cost-group-width', `${costGroupWidth}px`);

  //Update pnl group header width (cash + percent + padding for header)
  const pnlGroupWidth = dynamicWidths.value.pnlCash + 50; //50px for percentage
  document.documentElement.style.setProperty('--pnl-group-width', `${pnlGroupWidth}px`);

  //Update value group header width (just value column)
  const valueGroupWidth = dynamicWidths.value.value;
  document.documentElement.style.setProperty('--value-group-width', `${valueGroupWidth}px`);
}

function onClickToggleOrders() {
  showOrders.value = !showOrders.value;
}

function onClickClose() {
  appStore.hideUserPositionsDialog();
}

//Watch for dialog opening to fetch positions
watch(() => appStore.userPositionsDialog.isShown, (newVal) => {
  if (newVal) {
    loadDialog();
  }
});

//Watch for positions changes to recalculate widths
watch(positions, () => {
  calculateDynamicWidths();
}, { deep: true });
</script>

<style scoped lang="scss">
.dialog-positions {
  .positions-main {
    width: auto;
    height: auto;
    max-width: none !important;
    max-height: 90vh;
    min-height: 60vh;
    min-width: 650px;
    padding: 0;
    display: flex;
    flex-direction: column;
  }
}

.positions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-pnl {
  font-size: 16px;
  font-weight: 600;
  color: #666;
  margin-top: 2px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  flex-shrink: 0;
}

.user-avatar img {
  object-fit: cover;
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.profile-none {
  background-color: #333333;
  border-radius: 50%;
  width: 100%;
  height: 100%;
}

.btn-orders {
  color: #c5a030;
  background-color: #f2c94c1a;
  border-radius: 6px;
  padding: 6px 12px;
  font-weight: 600;
}

.orders-section {
  border-bottom: 1px solid #e0e0e0;
  padding: 16px 20px;
  background-color: #fafafa;
}

.positions-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.loading-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 20px;
}

.no-positions-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 20px;
  color: #999;
  font-size: 16px;
}

.positions-header-row {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  color: #666;
  position: sticky;
  top: 0;
  z-index: 1;
}

.header-icon-space {
  width: 62px;
  flex-shrink: 0;
}

.header-data-section {
  flex: 1;
  display: flex;
  align-items: center;
}

.header-shares-group {
  width: var(--shares-group-width, 115px);
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 0 6px; //Padding applied to header instead of individual columns
}

.header-price-group {
  width: var(--price-group-width, 116px); /* 40px + 16px + 60px = avgprice + arrow + curprice */
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.header-cost-group {
  width: var(--cost-group-width, 60px);
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 0 3px; //Padding applied to header instead of individual columns
}

.header-pnl-group {
  width: var(--pnl-group-width, 100px);
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 0 1px; //Padding applied to header instead of individual columns
}

.header-value-group {
  width: var(--value-group-width, 60px);
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 0 15px; //Padding applied to header instead of individual columns
}

.sort-active {
  color: #1976d2;
}

.position-group {
  border-bottom: 1px solid #e0e0e0;
}

.position-container {
  display: flex;
  flex-direction: row;
  padding: 0 16px;
  background-color: #fafafa;
}

.position-icon {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  padding-top: 4px;

  img {
    width: 42px;
    height: 42px;
    border-radius: 4px;
    object-fit: cover;
  }
}

.data-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-left: 12px;
}

.market-title-link {
  text-decoration: none;
  color: inherit;

  &:hover {
    text-decoration: none;
  }
}

.market-title {
  font-weight: 600;
  font-size: 14px;
  color: #333;
  padding: 4px 0 4px 0;

  &:hover {
    color: #1976d2;
    cursor: pointer;
  }
}

.position-data-row {
  display: flex;
  align-items: center;
  height: 24px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 13px;

  &:hover {
    background-color: #f9f9f9;
  }

  &:last-child {
    border-bottom: none;
  }
}

//Base styling for all row cells - 100% height with vertical centering
[class^="row-"] {
  height: 100%;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.row-outcome {
  width: var(--outcome-width, 60px);
  font-weight: 600;
  justify-content: center;
}

.row-avgprice {
  width: 40px;
  font-family: monospace;
  font-weight: 600;
  justify-content: left;
}

.row-arrow {
  width: 16px;
  color: #b7b7b7;
  font-size: 12px;
  font-weight: bold;
  justify-content: center;
}

.row-curprice {
  width: 60px;
  font-family: monospace;
  font-size: 14px;
  justify-content: left;
}

.row-shares {
  width: var(--shares-width, 55px);
  font-family: monospace;
  font-weight: 600;
  justify-content: flex-end;
}

.row-cost {
  width: var(--cost-width, 60px);
  font-family: monospace;
  color: #717171;
  justify-content: flex-end;
  border-right: 1px solid #e0e0e0;
}

.row-pnl-cash {
  width: var(--pnl-cash-width, 50px);
  font-family: monospace;
  font-weight: 600;
  justify-content: flex-end;
}

.row-pnl-percent {
  width: 50px;
  font-family: monospace;
  font-weight: 600;
  font-size: 9px;
  margin-top: 2px;
  justify-content: flex-start;
  border-right: 1px solid #e0e0e0;
}

.row-value {
  width: var(--value-width, 60px);
  font-family: monospace;
  justify-content: flex-end;
}
</style>

<style>
.q-dialog__backdrop {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}
</style>
